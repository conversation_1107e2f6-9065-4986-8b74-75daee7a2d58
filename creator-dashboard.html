<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LawVriksh - Creator Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baskerville+Old+Face&family=Merriweather:wght@400;500;600;700&family=Josefin+Sans:ital@1&family=Battambang:wght@400;700&family=Source+Sans+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --background: hsl(30, 15%, 96%);
            --foreground: hsl(30, 10%, 15%);
            --card: hsl(0, 0%, 100%);
            --card-foreground: hsl(30, 10%, 15%);
            --primary: hsl(28, 84%, 44%);
            --primary-foreground: hsl(0, 0%, 100%);
            --secondary: hsl(35, 25%, 88%);
            --secondary-foreground: hsl(30, 10%, 25%);
            --muted: hsl(30, 15%, 92%);
            --muted-foreground: hsl(30, 8%, 45%);
            --accent: hsl(35, 80%, 55%);
            --accent-foreground: hsl(0, 0%, 100%);
            --border: hsl(30, 15%, 88%);
            --legal-gold: hsl(42, 88%, 55%);
            --legal-bronze: hsl(28, 65%, 35%);
            --legal-cream: hsl(45, 35%, 95%);
            --legal-text: hsl(30, 10%, 20%);
            --success: hsl(142, 76%, 36%);
            --warning: hsl(38, 92%, 50%);
            --error: hsl(0, 84%, 60%);
        }

        body {
            font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            line-height: 1.6;
        }

        .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            height: 64px;
            background-color: var(--card);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .brand-name {
            font-family: 'Baskerville Old Face', serif;
            font-weight: 600;
            font-size: 1.5rem;
            color: var(--legal-text);
        }

        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-profile {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--legal-gold);
            border: 2px solid var(--border);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        /* Main Layout */
        .main-layout {
            display: flex;
            flex: 1;
            padding-top: 64px;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--card);
            border-right: 1px solid var(--border);
            padding: 24px 0;
            position: fixed;
            height: calc(100vh - 64px);
            overflow-y: auto;
        }

        .sidebar-nav {
            padding: 0 24px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin-bottom: 4px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--legal-text);
            text-decoration: none;
        }

        .nav-item:hover, .nav-item.active {
            background-color: var(--muted);
            color: var(--legal-bronze);
        }

        .nav-item svg {
            width: 20px;
            height: 20px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 32px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-family: 'Baskerville Old Face', serif;
            font-size: 2rem;
            font-weight: bold;
            color: var(--legal-text);
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: var(--muted-foreground);
            font-size: 1rem;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background-color: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-title {
            font-family: 'Battambang', sans-serif;
            font-size: 0.875rem;
            font-weight: 700;
            color: var(--muted-foreground);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-icon {
            width: 24px;
            height: 24px;
            color: var(--legal-bronze);
        }

        .stat-value {
            font-family: 'Baskerville Old Face', serif;
            font-size: 2rem;
            font-weight: bold;
            color: var(--legal-text);
            margin-bottom: 8px;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--success);
        }

        .stat-change.negative {
            color: var(--error);
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 32px;
        }

        .content-section {
            background-color: var(--card);
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--legal-text);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--border);
        }

        /* Content List */
        .content-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border);
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-thumbnail {
            width: 60px;
            height: 60px;
            background-color: var(--muted);
            border-radius: 8px;
            flex-shrink: 0;
        }

        .content-info {
            flex: 1;
        }

        .content-title {
            font-weight: 600;
            color: var(--legal-text);
            margin-bottom: 4px;
            font-size: 0.875rem;
        }

        .content-meta {
            font-size: 0.75rem;
            color: var(--muted-foreground);
        }

        .content-stats {
            display: flex;
            gap: 16px;
            font-size: 0.75rem;
            color: var(--muted-foreground);
        }

        /* Buttons */
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-family: 'Merriweather', serif;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--legal-bronze);
            color: white;
        }

        .btn-primary:hover {
            background: var(--legal-text);
        }

        .btn-secondary {
            background: transparent;
            color: var(--legal-text);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background: var(--muted);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="brand-name">LawVriksh Creator Studio</div>
            <div class="header-right">
                <div class="user-profile">JD</div>
            </div>
        </header>

        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar">
                <nav class="sidebar-nav">
                    <a href="#" class="nav-item active">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Dashboard
                    </a>
                    <a href="#" class="nav-item">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Content
                    </a>
                    <a href="#" class="nav-item">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        Monetization
                    </a>
                    <a href="#" class="nav-item">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Analytics
                    </a>
                    <a href="#" class="nav-item">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        Subscribers
                    </a>
                    <a href="#" class="nav-item">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Settings
                    </a>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">Creator Dashboard</h1>
                    <p class="page-subtitle">Monitor your content performance and earnings</p>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Total Likes</span>
                            <svg class="stat-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                        <div class="stat-value">2,847</div>
                        <div class="stat-change positive">
                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                            </svg>
                            +12.5% from last month
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Avg Reading Time</span>
                            <svg class="stat-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="stat-value">4.2 min</div>
                        <div class="stat-change positive">
                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                            </svg>
                            +8.3% from last month
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Credits Earned</span>
                            <svg class="stat-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </div>
                        <div class="stat-value">₹18,450</div>
                        <div class="stat-change positive">
                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                            </svg>
                            +23.1% from last month
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Premium Subscribers</span>
                            <svg class="stat-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <div class="stat-value">342</div>
                        <div class="stat-change positive">
                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                            </svg>
                            +15.7% from last month
                        </div>
                    </div>
                </div>

                <!-- Content Grid -->
                <div class="content-grid">
                    <!-- Recent Content -->
                    <div class="content-section">
                        <h2 class="section-title">Recent Content</h2>
                        
                        <div class="content-item">
                            <div class="content-thumbnail"></div>
                            <div class="content-info">
                                <div class="content-title">Understanding India's Digital Personal Data Protection Act</div>
                                <div class="content-meta">Published 2 days ago</div>
                                <div class="content-stats">
                                    <span>1,247 views</span>
                                    <span>89 likes</span>
                                    <span>₹2,340 earned</span>
                                </div>
                            </div>
                        </div>

                        <div class="content-item">
                            <div class="content-thumbnail"></div>
                            <div class="content-info">
                                <div class="content-title">Corporate Law Updates: New Compliance Requirements</div>
                                <div class="content-meta">Published 5 days ago</div>
                                <div class="content-stats">
                                    <span>892 views</span>
                                    <span>67 likes</span>
                                    <span>₹1,780 earned</span>
                                </div>
                            </div>
                        </div>

                        <div class="content-item">
                            <div class="content-thumbnail"></div>
                            <div class="content-info">
                                <div class="content-title">Intellectual Property Rights in the Digital Age</div>
                                <div class="content-meta">Published 1 week ago</div>
                                <div class="content-stats">
                                    <span>1,456 views</span>
                                    <span>123 likes</span>
                                    <span>₹2,912 earned</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 20px;">
                            <a href="#" class="btn btn-primary">Create New Content</a>
                            <a href="#" class="btn btn-secondary">View All Content</a>
                        </div>
                    </div>

                    <!-- Monetization Overview -->
                    <div class="content-section">
                        <h2 class="section-title">Monetization Suite</h2>
                        
                        <div style="margin-bottom: 24px;">
                            <h3 style="font-family: 'Battambang', sans-serif; font-weight: 700; margin-bottom: 12px; color: var(--legal-text);">Credit-Based Economy</h3>
                            <p style="font-family: 'Source Sans Pro', sans-serif; font-size: 0.875rem; color: var(--muted-foreground); margin-bottom: 16px;">Enabling micro-transactions and content monetization</p>
                            <div style="background: var(--legal-cream); padding: 16px; border-radius: 6px; border-left: 4px solid var(--legal-gold);">
                                <div style="font-weight: 600; color: var(--legal-text);">Current Balance: ₹18,450</div>
                                <div style="font-size: 0.875rem; color: var(--muted-foreground);">Available for withdrawal</div>
                            </div>
                        </div>

                        <div style="margin-bottom: 24px;">
                            <h3 style="font-family: 'Battambang', sans-serif; font-weight: 700; margin-bottom: 12px; color: var(--legal-text);">Subscription Management</h3>
                            <p style="font-family: 'Source Sans Pro', sans-serif; font-size: 0.875rem; color: var(--muted-foreground); margin-bottom: 16px;">Tiered access and premium content</p>
                            <div style="display: flex; gap: 12px;">
                                <div style="flex: 1; background: var(--card); border: 1px solid var(--border); padding: 12px; border-radius: 6px; text-align: center;">
                                    <div style="font-weight: 600; color: var(--legal-text);">Free Tier</div>
                                    <div style="font-size: 0.875rem; color: var(--muted-foreground);">1,205 subscribers</div>
                                </div>
                                <div style="flex: 1; background: var(--card); border: 1px solid var(--border); padding: 12px; border-radius: 6px; text-align: center;">
                                    <div style="font-weight: 600; color: var(--legal-text);">Premium</div>
                                    <div style="font-size: 0.875rem; color: var(--muted-foreground);">342 subscribers</div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 24px;">
                            <h3 style="font-family: 'Battambang', sans-serif; font-weight: 700; margin-bottom: 12px; color: var(--legal-text);">Analytics Dashboard</h3>
                            <p style="font-family: 'Source Sans Pro', sans-serif; font-size: 0.875rem; color: var(--muted-foreground); margin-bottom: 16px;">Tracking performance and engagement</p>
                            <div style="background: var(--muted); padding: 16px; border-radius: 6px;">
                                <div style="font-size: 0.875rem; color: var(--muted-foreground);">This month's performance</div>
                                <div style="font-weight: 600; color: var(--legal-text);">↗ 23.1% increase in earnings</div>
                            </div>
                        </div>

                        <div>
                            <h3 style="font-family: 'Battambang', sans-serif; font-weight: 700; margin-bottom: 12px; color: var(--legal-text);">Revenue Optimization</h3>
                            <p style="font-family: 'Source Sans Pro', sans-serif; font-size: 0.875rem; color: var(--muted-foreground); margin-bottom: 16px;">Maximizing earning potential across channels</p>
                            <a href="#" class="btn btn-primary" style="width: 100%; justify-content: center;">Optimize Revenue</a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
