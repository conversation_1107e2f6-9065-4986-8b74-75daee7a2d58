import Button from "./Button";
import "./Homepage.css";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import Navbar from "./Navbar";
import WhyUs from "./WhyUs";
import WaitlistPopup from "./WaitlistPopup";
import Footer from "./Footer";
import { useState, useEffect } from "react";

// Register ScrollTrigger and ScrollToPlugin
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

export default function Homepage() {
  const [isWaitlistPopupOpen, setIsWaitlistPopupOpen] = useState(false);

  // State tracking for section animation completion and scroll direction
  const [sectionAnimated, setSectionAnimated] = useState({
    homepage: false,
    homepage2: false,
    homepage3: false,
    homepage4: false,
    teamTestimonial: false,
    cta: false
  });

  const [scrollDirection, setScrollDirection] = useState('down');
  const [lastScrollY, setLastScrollY] = useState(0);

  const handleJoinWaitlist = () => {
    setIsWaitlistPopupOpen(true);
  };

  const handleCloseWaitlistPopup = () => {
    setIsWaitlistPopupOpen(false);
  };

  // Smooth scrolling implementation and scroll direction tracking
  useEffect(() => {
    // Track scroll direction
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      if (currentScrollY > lastScrollY) {
        setScrollDirection('down');
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
      }
      setLastScrollY(currentScrollY);
    };

    // Create smooth scrolling for the entire page
    const smoothScroll = (target: number, duration: number = 1.5) => {
      gsap.to(window, {
        duration: duration,
        scrollTo: { y: target, autoKill: true },
        ease: "power2.inOut"
      });
    };

    // Handle smooth scrolling for anchor links and navigation
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const targetId = target.getAttribute('href')?.substring(1);
        const targetElement = document.getElementById(targetId || '');
        if (targetElement) {
          const targetPosition = targetElement.offsetTop;
          smoothScroll(targetPosition);
        }
      }
    };

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('click', handleSmoothScroll);

    // Custom smooth scroll for wheel events (optional - for ultra-smooth experience)
    let isScrolling = false;
    const handleWheel = (e: WheelEvent) => {
      if (isScrolling) return;

      e.preventDefault();
      isScrolling = true;

      const currentScroll = window.pageYOffset;
      const delta = e.deltaY;
      const scrollAmount = delta > 0 ? 100 : -100;
      const targetScroll = Math.max(0, currentScroll + scrollAmount);

      gsap.to(window, {
        duration: 0.8,
        scrollTo: { y: targetScroll, autoKill: true },
        ease: "power2.out",
        onComplete: () => {
          isScrolling = false;
        }
      });
    };

    // Uncomment the line below for ultra-smooth wheel scrolling (optional)
    // document.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleSmoothScroll);
      // document.removeEventListener('wheel', handleWheel);
    };
  }, [lastScrollY]);

  // Effect to recreate ScrollTriggers when animation state or scroll direction changes
  useEffect(() => {
    ScrollTrigger.refresh();
  }, [sectionAnimated, scrollDirection]);

  // Handle window resize for responsive animations
  useEffect(() => {
    const handleResize = () => {
      // Refresh ScrollTrigger on resize
      ScrollTrigger.refresh();

      // Refresh features section animations on resize
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger && trigger.trigger.classList.contains('homepage2')) {
          trigger.kill();
        }
      });

      // Features scroll trigger will be recreated in useGSAP
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sectionAnimated.homepage2]);

  useGSAP(() => {
    // Initialize all animated elements to hidden state
    gsap.set([
      ".homepage__hero",
      ".homepage__hero-title",
      ".homepage__hero-word",
      ".homepage__main-title",
      ".homepage__main-content",
      ".homepage__feature-item"
    ], {
      opacity: 0,
      y: 50,
      x: 0
    });

    // Set responsive initial positions for hero elements
    const getResponsiveOffset = () => {
      const vw = window.innerWidth;
      if (vw <= 767) {
        return { large: 50, medium: 30, small: 20 };
      } else if (vw <= 1024) {
        return { large: 100, medium: 60, small: 40 };
      } else {
        return { large: 800, medium: 100, small: 50 };
      }
    };

    const offsets = getResponsiveOffset();

    gsap.set(".homepage__hero", { x: offsets.large, y: 0 });
    gsap.set(".homepage__hero-title", { x: -offsets.large, y: 0 });
    gsap.set(".homepage__hero-word", { x: offsets.small, y: 0 });
    gsap.set(".homepage__main-title", { x: offsets.medium, y: 0 });
    gsap.set(".homepage__main-content", { x: offsets.medium, y: 0 });
    gsap.set(".homepage__feature-item", { y: offsets.medium, x: 0 });

    // Section 1: Hero Section with Scroll-Back Optimization
    const createHeroScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage",
        start: "top top",
        end: sectionAnimated.homepage ? "bottom top" : "+=100%",
        pin: !sectionAnimated.homepage && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.homepage && scrollDirection === 'down') {
            // Hero section animations sequence (only on first visit scrolling down)
            const tl = gsap.timeline({
              onComplete: () => {
                setSectionAnimated(prev => ({ ...prev, homepage: true }));
                // Refresh ScrollTrigger after animation completes
                ScrollTrigger.refresh();
              }
            });

            tl.to(".homepage__hero", {
              duration: 1,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            })
            .to(".homepage__hero-title", {
              duration: 1,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            }, "-=0.5")
            .to(".homepage__hero-word", {
              duration: 0.8,
              opacity: 1,
              x: 0,
              stagger: 0.3,
              ease: "power2.out"
            }, "-=0.3")
            .to(".homepage__main-title", {
              duration: 1,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            }, "-=0.5")
            .to(".homepage__main-content", {
              duration: 1,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            }, "-=0.7")
            .to(".homepage__feature-item", {
              duration: 0.8,
              opacity: 1,
              y: 0,
              stagger: 0.2,
              ease: "power2.out"
            }, "-=0.5");
          }
        }
      });
    };

    createHeroScrollTrigger();

    // Features Section (homepage2) Scroll-Triggered Image Carousel with Scroll-Back Optimization
    const createFeaturesScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage2",
        start: "top top",
        end: sectionAnimated.homepage2 ? "bottom top" : "+=300%",
        pin: !sectionAnimated.homepage2 && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.homepage2 && scrollDirection === 'down') {
            // Features section scroll-triggered image carousel (only on first visit scrolling down)
            // Set initial state for features and content
            gsap.set([".feature1", ".feature2", ".feature3", ".feature4"], { opacity: 0 });
            gsap.set([".feature-content-1", ".feature-content-2", ".feature-content-3", ".feature-content-4"], { opacity: 0 });
            gsap.set(".feature1", { opacity: 1 }); // Show first feature initially
            gsap.set(".feature-content-1", { opacity: 1 }); // Show first content initially

            // Create scroll-triggered image transitions
            ScrollTrigger.create({
              trigger: ".homepage2",
              start: "top top",
              end: "+=300%",
              pin: true,
              scrub: 1,
              onUpdate: (self) => {
                const progress = self.progress;
                console.log('Features scroll progress:', progress);

                // Hide all features and content initially
                gsap.set([".feature1", ".feature2", ".feature3", ".feature4"], { opacity: 0 });
                gsap.set([".feature-content-1", ".feature-content-2", ".feature-content-3", ".feature-content-4"], { opacity: 0 });

                // Show features and content based on scroll progress
                if (progress < 0.25) {
                  // Show feature 1 and its content
                  gsap.set(".feature1", { opacity: 1 });
                  gsap.set(".feature-content-1", { opacity: 1 });
                  console.log('Showing feature 1');
                } else if (progress < 0.5) {
                  // Show feature 2 and its content
                  gsap.set(".feature2", { opacity: 1 });
                  gsap.set(".feature-content-2", { opacity: 1 });
                  console.log('Showing feature 2');
                } else if (progress < 0.75) {
                  // Show feature 3 and its content
                  gsap.set(".feature3", { opacity: 1 });
                  gsap.set(".feature-content-3", { opacity: 1 });
                  console.log('Showing feature 3');
                } else {
                  // Show feature 4 and its content
                  gsap.set(".feature4", { opacity: 1 });
                  gsap.set(".feature-content-4", { opacity: 1 });
                  console.log('Showing feature 4');
                }

                // Mark as completed when fully scrolled
                if (progress >= 1) {
                  setSectionAnimated(prev => ({ ...prev, homepage2: true }));
                }
              }
            });
          }
        }
      });
    };

    createFeaturesScrollTrigger();

    // Initialize Why Us section elements to hidden state with responsive offsets
    gsap.set([
      ".homepage__hero1",
      ".homepage__why-us-features .homepage__feature-item-container"
    ], {
      opacity: 0
    });

    const whyUsOffsets = getResponsiveOffset();
    gsap.set(".homepage__hero1", { x: whyUsOffsets.large * 0.5 });
    gsap.set(".homepage__why-us-features .homepage__feature-item-container", { y: whyUsOffsets.small });

    // Section 3: Why Us Section with Scroll-Back Optimization
    const createWhyUsScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage3",
        start: "top top",
        end: sectionAnimated.homepage3 ? "bottom top" : "+=100%",
        pin: !sectionAnimated.homepage3 && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.homepage3 && scrollDirection === 'down') {
            // Why Us section animations sequence (only on first visit scrolling down)
            const tl = gsap.timeline({
              onComplete: () => {
                setSectionAnimated(prev => ({ ...prev, homepage3: true }));
                ScrollTrigger.refresh();
              }
            });

            tl.to(".homepage__hero1", {
              duration: 1.2,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            })
            .to(".homepage__why-us-features .homepage__feature-item-container", {
              duration: 0.8,
              opacity: 1,
              y: 0,
              stagger: 0.3,
              ease: "power2.out"
            }, "-=0.5");
          }
        }
      });
    };

    createWhyUsScrollTrigger();

    // Initialize Testimonials section elements to hidden state with responsive offsets
    gsap.set([
      ".testimonialimage",
      ".quote-word",
      ".homepage__testimonials-title"
    ], {
      opacity: 0
    });

    const testimonialsOffsets = getResponsiveOffset();
    gsap.set(".testimonialimage", { scale: 0.8 });
    gsap.set(".quote-word", { y: testimonialsOffsets.small });
    gsap.set(".homepage__testimonials-title", { y: testimonialsOffsets.small * 1.5 });

    // Section 4: Testimonials Section with Scroll-Back Optimization
    const createTestimonialsScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage4",
        start: "top top",
        end: sectionAnimated.homepage4 ? "bottom top" : "+=100%",
        pin: !sectionAnimated.homepage4 && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.homepage4 && scrollDirection === 'down') {
            // Testimonials section animations sequence (only on first visit scrolling down)
            const tl = gsap.timeline({
              onComplete: () => {
                setSectionAnimated(prev => ({ ...prev, homepage4: true }));
                ScrollTrigger.refresh();
              }
            });

            tl.to(".homepage__testimonials-title", {
              duration: 0.8,
              opacity: 1,
              y: 0,
              ease: "power2.out"
            })
            .to(".testimonialimage", {
              duration: 1.2,
              opacity: 1,
              scale: 1,
              ease: "power2.out"
            }, "-=0.3")
            .to(".quote-word", {
              duration: 0.6,
              opacity: 1,
              y: 0,
              stagger: 0.1,
              ease: "power2.out"
            }, "-=0.5");
          }
        }
      });
    };

    createTestimonialsScrollTrigger();

    // Initialize Team Testimonial section elements to hidden state with responsive offsets
    gsap.set([
      ".homepage__team-testimonial-background",
      ".homepage__team-testimonial-title",
      ".homepage__team-testimonial-quote",
      ".homepage__team-testimonial-author",
      ".homepage__team-testimonial-star"
    ], {
      opacity: 0
    });

    const teamTestimonialOffsets = getResponsiveOffset();
    gsap.set(".homepage__team-testimonial-background", { scale: 1.1 });
    gsap.set(".homepage__team-testimonial-title", { x: -teamTestimonialOffsets.medium });
    gsap.set(".homepage__team-testimonial-quote", { y: teamTestimonialOffsets.small * 2.5 });
    gsap.set(".homepage__team-testimonial-author", { x: -teamTestimonialOffsets.small * 2.5 });
    gsap.set(".homepage__team-testimonial-star", { scale: 0, rotation: 180 });

    // Section 5: Team Testimonial Section with Scroll-Back Optimization
    const createTeamTestimonialScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage__team-testimonial-section",
        start: "top top",
        end: sectionAnimated.teamTestimonial ? "bottom top" : "+=100%",
        pin: !sectionAnimated.teamTestimonial && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.teamTestimonial && scrollDirection === 'down') {
            // Team testimonial section animations sequence (only on first visit scrolling down)
            const tl = gsap.timeline({
              onComplete: () => {
                setSectionAnimated(prev => ({ ...prev, teamTestimonial: true }));
                ScrollTrigger.refresh();
              }
            });

            tl.to(".homepage__team-testimonial-background", {
              duration: 1.5,
              opacity: 1,
              scale: 1,
              ease: "power2.out"
            })
            .to(".homepage__team-testimonial-title", {
              duration: 1,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            }, "-=1")
            .to(".homepage__team-testimonial-quote", {
              duration: 1.2,
              opacity: 1,
              y: 0,
              ease: "power2.out"
            }, "-=0.8")
            .to(".homepage__team-testimonial-author", {
              duration: 0.8,
              opacity: 1,
              x: 0,
              ease: "power2.out"
            }, "-=0.6")
            .to(".homepage__team-testimonial-star", {
              duration: 0.6,
              opacity: 1,
              scale: 1,
              rotation: 0,
              stagger: 0.1,
              ease: "back.out(1.7)"
            }, "-=0.4");
          }
        }
      });
    };

    createTeamTestimonialScrollTrigger();

    // Initialize CTA section elements to hidden state with responsive offsets
    gsap.set([
      ".homepage__cta-title",
      ".homepage__cta-description",
      ".homepage__cta-button",
      ".homepage__cta-image"
    ], {
      opacity: 0
    });

    const ctaOffsets = getResponsiveOffset();
    gsap.set(".homepage__cta-title", { y: ctaOffsets.medium });
    gsap.set(".homepage__cta-description", { y: ctaOffsets.small * 2.5 });
    gsap.set(".homepage__cta-button", { scale: 0.8 });
    gsap.set(".homepage__cta-image", { x: ctaOffsets.large * 0.25, scale: 1.1 });

    // Section 6: CTA Section with Scroll-Back Optimization
    const createCTAScrollTrigger = () => {
      ScrollTrigger.create({
        trigger: ".homepage__cta-section",
        start: "top top",
        end: sectionAnimated.cta ? "bottom top" : "+=100%",
        pin: !sectionAnimated.cta && scrollDirection === 'down',
        scrub: false,
        onEnter: () => {
          if (!sectionAnimated.cta && scrollDirection === 'down') {
            // CTA section animations sequence (only on first visit scrolling down)
            const tl = gsap.timeline({
              onComplete: () => {
                setSectionAnimated(prev => ({ ...prev, cta: true }));
                ScrollTrigger.refresh();
              }
            });

            tl.to(".homepage__cta-title", {
              duration: 1.2,
              opacity: 1,
              y: 0,
              ease: "power2.out"
            })
            .to(".homepage__cta-description", {
              duration: 1,
              opacity: 1,
              y: 0,
              ease: "power2.out"
            }, "-=0.8")
            .to(".homepage__cta-button", {
              duration: 0.8,
              opacity: 1,
              scale: 1,
              ease: "back.out(1.7)"
            }, "-=0.6")
            .to(".homepage__cta-image", {
              duration: 1.5,
              opacity: 1,
              x: 0,
              scale: 1,
              ease: "power2.out"
            }, "-=1");
          }
        }
      });
    };

    createCTAScrollTrigger();

    // Footer animations (non-pinned, traditional scroll trigger)
  });

  // Smooth full-page scroll functionality

  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118",
      title: "Features",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118",
      title: "Why You should Join us",
      description: "Urgency of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--medium",
      contentClass: "homepage__feature-content homepage__feature-content--wide",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Testimonials",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--small",
    },
  ];

  return (
    <div>
      <div id="home" className="homepage">
        <Navbar onJoinWaitlist={handleJoinWaitlist} />
        <div className="homepage__main-wrapper">
          <div className="homepage__hero-title">
            <span className="homepage__hero-word">Be</span>{" "}
            <span className="homepage__hero-word">a</span>{" "}
            <span className="homepage__hero-word">legal</span> <br />
            <span className="homepage__hero-word">entrepreneur</span>
          </div>

          <div className="homepage__features-section">
            {features.map((feature, index) => (
              <div key={index} className="homepage__feature-item">
                <img src={feature.icon} alt="" className={feature.iconClass} />
                <div
                  className={
                    feature.contentClass || "homepage__feature-content"
                  }
                >
                  <div className="homepage__feature-title">{feature.title}</div>
                  <div className="homepage__feature-description">
                    {feature.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="homepage__hero">
          <div className="homepage__main-section">
            <div className="homepage__main-title">
              Creator platform for modern
              <br />
              --- legal professionals
            </div>

            <div className="homepage__main-content">
              <div className="homepage__main-quote">
                &ldquo;LawVriksh is the first AI-powered platform that helps
                legal experts build a respected online voice, create high-impact
                content, and unlock new monetization opportunities.&rdquo;
              </div>
              <Button size="large" onClick={handleJoinWaitlist}>Join Waitlist</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-line"></div>
          <div className="homepage__divider-text">
            <span className="homepage__divider-word">Explore</span>
            <span className="homepage__divider-word">Our</span>
            <span className="homepage__divider-word">Features</span>
          </div>
          <div className="homepage__divider-line"></div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      <div id="features" className="homepage2">
        <div className="feature1"></div>
        <div className="feature2"></div>
        <div className="feature3"></div>
        <div className="feature4"></div>

        <div className="homepage__content-engine-content feature-content-1">
          <div className="homepage__content-engine-title">
            <div className="homepage__content-engine-title-text">
              Content Creation
              <br /> &amp; Research <br />
              Engine
            </div>
          </div>
          <div className="homepage__content-engine-description">
            <div className="homepage__content-engine-description-text">
              AI-Powered Writing Tools with legal-specific training and
              compliance checks
            </div>
          </div>
        </div>

        <div className="homepage__content-engine-content feature-content-2">
          <div className="homepage__content-engine-title">
            <div className="homepage__content-engine-title-text">
              Content Quality
              <br /> &amp; Accuracy <br />
              Platform
            </div>
          </div>
          <div className="homepage__content-engine-description">
            <div className="homepage__content-engine-description-text">
              Rigorous editorial standards and peer review ensure factual accuracy and practical relevance
            </div>
          </div>
        </div>

        <div className="homepage__content-engine-content feature-content-3">
          <div className="homepage__content-engine-title">
            <div className="homepage__content-engine-title-text">
              Legal Community
              <br /> &amp; Networking <br />
              Hub
            </div>
          </div>
          <div className="homepage__content-engine-description">
            <div className="homepage__content-engine-description-text">
              Build dynamic community for dialogue between professionals and the public
            </div>
          </div>
        </div>

        <div className="homepage__content-engine-content feature-content-4">
          <div className="homepage__content-engine-title">
            <div className="homepage__content-engine-title-text">
              Expert Digital
              <br /> Presence <br />
              Amplifier
            </div>
          </div>
          <div className="homepage__content-engine-description">
            <div className="homepage__content-engine-description-text">
              Amplify digital footprint of contributing legal experts with detailed author profiles
            </div>
          </div>
        </div>

      </div>
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-line"></div>
          <div className="homepage__divider-text">
            <span className="homepage__divider-word">Explore</span>
            <span className="homepage__divider-word">Our</span>
            <span className="homepage__divider-word">Features</span>
          </div>
          <div className="homepage__divider-line"></div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>
      <div id="why-us" className="homepage3">
        <WhyUs />
      </div>
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-line"></div>
          <div className="homepage__divider-text">
            <span className="homepage__divider-word">Explore</span>
            <span className="homepage__divider-word">Our</span>
            <span className="homepage__divider-word">Features</span>
          </div>
          <div className="homepage__divider-line"></div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>
      <div id="testimonials" className="homepage4">
        <div className="homepage__testimonials-content">
          <div className="homepage__testimonials-header">
            <div className="homepage__testimonials-title">
              Our Customer's Opinions
            </div>
            <div className="homepage__testimonials-quote-mark"></div>
          </div>
          <div className="homepage__testimonials-quote-box">
            <div className="homepage__testimonials-quote-text">
              <span className="quote-word">Extensive</span>{" "}
              <span className="quote-word">substantive</span>{" "}
              <span className="quote-word">knowledge,</span>{" "}
              <span className="quote-word">extensive</span>{" "}
              <span className="quote-word">experience,</span>{" "}
              <span className="quote-word">reliability,</span>{" "}
              <span className="quote-word">commitment</span>{" "}
              <span className="quote-word">and</span>{" "}
              <span className="quote-word">availability</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">a</span>{" "}
              <span className="quote-word">team</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">specialists</span>{" "}
              <span className="quote-word">ensure</span>{" "}
              <span className="quote-word">the</span>{" "}
              <span className="quote-word">highest</span>{" "}
              <span className="quote-word">level</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">service.</span>
            </div>
          </div>
        </div>
        <div className="homepage__hero2">
          <div className="testimonialimage"></div>
          <div className="homepage__subhero"></div>
        </div>
      </div>
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-line"></div>
          <div className="homepage__divider-text">
            <span className="homepage__divider-word">Explore</span>
            <span className="homepage__divider-word">Our</span>
            <span className="homepage__divider-word">Features</span>
          </div>
          <div className="homepage__divider-line"></div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>
      <div className="homepage__team-testimonial-section">
        <div className="homepage__team-testimonial-background">
          <div
            dangerouslySetInnerHTML={{
              __html:
                '<svg id="210:579" width="799" height="871" viewBox="0 0 799 871" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="background-svg" style="width: 799px; height: 871px; display: block"> <foreignObject x="0" y="0" width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(0px);clip-path:url(#bgblur_0_210_579_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="0" d="M0 0H525V871H0V0Z" fill="#E5CCA4"></path> <foreignObject x="340.3" y="128.8" width="469.4" height="613.4"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5.35px);clip-path:url(#bgblur_1_210_579_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="10.7" d="M351 139.5H799V731.5H351V139.5Z" fill="#F9EDE9"></path> <rect x="351" y="139.5" width="448" height="592" fill="url(#pattern0_210_579)"></rect> <defs> <clipPath id="bgblur_0_210_579_clip_path" transform="translate(0 0)"><path d="M0 0H525V871H0V0Z"></path> </clipPath><clipPath id="bgblur_1_210_579_clip_path" transform="translate(-340.3 -128.8)"><path d="M351 139.5H799V731.5H351V139.5Z"></path> </clipPath><pattern id="pattern0_210_579" patternContentUnits="objectBoundingBox" width="1" height="1"> <use xlink:href="#image0_210_579" transform="matrix(0.00172797 0 0 0.00130828 -0.426075 -0.278796)"></use> </pattern>  </defs> </svg>',
            }}
          />
        </div>
        <div className="homepage__team-testimonial-content">
          <div className="homepage__team-testimonial-ratings">
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/862c345882334d9eb32a40f2d45f9ec9b9265f5e?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
          </div>
          <div className="homepage__team-testimonial-text-content">
            <div className="homepage__team-testimonial-title">
              What our team has to say!
            </div>
            <div className="homepage__team-testimonial-quote">
              &quot;Amplify the digital footprint of contributing legal experts.
              Detailed author profiles and multi-channel promotion boost their
              professional authority.&quot;
            </div>
          </div>
          <div className="homepage__team-testimonial-attribution">
            <div className="homepage__team-testimonial-author">
              This is Sahil Saurav. Meet the Whole team
            </div>
          </div>
        </div>
      </div>
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-line"></div>
          <div className="homepage__divider-text">
            <span className="homepage__divider-word">Explore</span>
            <span className="homepage__divider-word">Our</span>
            <span className="homepage__divider-word">Features</span>
          </div>
          <div className="homepage__divider-line"></div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>
      <div id="contact" className="homepage__cta-section">
        <div className="homepage__hero3">
          <div className="homepage__cta-content">
            <div className="homepage__cta-title">
              Last Title of Call to action title for the Reader.
            </div>
            <div className="homepage__cta-description">
              A short Description supporting the title blah blah blhabl hab bklh
              blah blha blha blah .
            </div>
          </div>
          <Button onClick={handleJoinWaitlist}>
            <div className="homepage__cta-button-text">Join Waitlist</div>
          </Button>
        </div>
        <img
          src="https://api.builder.io/api/v1/image/assets/TEMP/2ea3e206fa40d2fb68cc408c333ea21056edef7a?width=2022"
          alt=""
          className="homepage__cta-image"
        />
      </div>

      <Footer />

      <WaitlistPopup
        isOpen={isWaitlistPopupOpen}
        onClose={handleCloseWaitlistPopup}
      />
    </div>
  );
}
