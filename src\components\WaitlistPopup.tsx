import React, { useState, useEffect } from 'react';
import gsap from 'gsap';
import Button from './Button';
import './WaitlistPopup.css';

interface WaitlistPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WaitlistPopup({ isOpen, onClose }: WaitlistPopupProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Animate popup in
      gsap.set('.waitlist-popup', { display: 'flex' });
      gsap.fromTo('.waitlist-popup__overlay', 
        { opacity: 0 },
        { opacity: 1, duration: 0.3, ease: 'power2.out' }
      );
      gsap.fromTo('.waitlist-popup__content',
        { scale: 0.8, opacity: 0, y: 50 },
        { scale: 1, opacity: 1, y: 0, duration: 0.4, ease: 'back.out(1.7)', delay: 0.1 }
      );
      
      // Animate form elements
      gsap.fromTo('.waitlist-popup__title',
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 0.5, delay: 0.3 }
      );
      gsap.fromTo('.waitlist-popup__description',
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.5, delay: 0.4 }
      );
      gsap.fromTo('.waitlist-popup__form-group',
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.4, delay: 0.5, stagger: 0.1 }
      );
      gsap.fromTo('.waitlist-popup__form-button',
        { opacity: 0, scale: 0.9 },
        { opacity: 1, scale: 1, duration: 0.4, delay: 0.7, ease: 'back.out(1.7)' }
      );
    } else {
      // Animate popup out
      gsap.to('.waitlist-popup__content',
        { scale: 0.8, opacity: 0, y: 50, duration: 0.3, ease: 'power2.in' }
      );
      gsap.to('.waitlist-popup__overlay',
        { 
          opacity: 0, 
          duration: 0.3, 
          delay: 0.1,
          onComplete: () => {
            gsap.set('.waitlist-popup', { display: 'none' });
          }
        }
      );
    }
  }, [isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setIsSubmitting(false);
    setIsSubmitted(true);

    // Show success animation
    gsap.fromTo('.waitlist-popup__success',
      { scale: 0, opacity: 0 },
      { scale: 1, opacity: 1, duration: 0.5, ease: 'back.out(1.7)' }
    );

    // Auto close after success
    setTimeout(() => {
      onClose();
      setIsSubmitted(false);
      setFormData({ name: '', email: '' });
    }, 2500);
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="waitlist-popup" onClick={handleOverlayClick}>
      <div className="waitlist-popup__overlay" />
      <div className="waitlist-popup__content">
        {!isSubmitted ? (
          <>
            <button className="waitlist-popup__close" onClick={onClose}>
              ×
            </button>
            
            <div className="waitlist-popup__header">
              <h2 className="waitlist-popup__title">Join the Waitlist</h2>
              <p className="waitlist-popup__description">
                Be among the first to experience LawVriksh's AI-powered platform for legal professionals. 
                Get early access and exclusive updates.
              </p>
            </div>

            <form className="waitlist-popup__form" onSubmit={handleSubmit}>
              <div className="waitlist-popup__form-group">
                <label htmlFor="name" className="waitlist-popup__label">Full Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="waitlist-popup__input"
                  required
                  placeholder="Enter your full name"
                />
              </div>

              <div className="waitlist-popup__form-group">
                <label htmlFor="email" className="waitlist-popup__label">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="waitlist-popup__input"
                  required
                  placeholder="Enter your email address"
                />
              </div>

              <div className="waitlist-popup__form-button">
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  size="large"
                >
                  {isSubmitting ? 'Joining...' : 'Join Waitlist'}
                </Button>
              </div>
            </form>
          </>
        ) : (
          <div className="waitlist-popup__success">
            <div className="waitlist-popup__success-icon">✓</div>
            <h3 className="waitlist-popup__success-title">Welcome to the Waitlist!</h3>
            <p className="waitlist-popup__success-message">
              Thank you for joining. We'll notify you when LawVriksh launches.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
